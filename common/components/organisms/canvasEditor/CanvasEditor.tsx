'use client'

import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
import Konva from 'konva';
import {
  Stage, Layer,
} from 'react-konva';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasHeader } from './CanvasHeader';
import { FloatingToolbar } from './FloatingToolbar';
import { cn } from '@/common/utils/helpers';
import { PLATFORM_CANVAS_SIZES } from '@/common/constants';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import toast from 'react-hot-toast';

export const addCursorHandlers = (node: Konva.Node) => {
  if (!node.draggable()) {
    return;
  }
  node.on('mouseover', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });

  node.on('mouseout', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'default';
    }
  });

  node.on('dragstart', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'grabbing';
    }
  });

  node.on('dragend', function (e) {
    const stage = e.target.getStage();
    if (stage && stage.container()) {
      stage.container().style.cursor = 'move';
    }
  });
};

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const stageRef = useRef<Konva.Stage>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const [konvaStage, setKonvaStage] = useState<Konva.Stage | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isManualZoom, setIsManualZoom] = useState(false);
  const [history, setHistory] = useState<string[]>([]);
  const [historyStep, setHistoryStep] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [stageSize, setStageSize] = useState<{ width: number; height: number }>(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    return {
      width: canvasSize.width, 
      height: canvasSize.height,
    };
  });
  const { activeProject } = useProjectContext();
  
  const updateStageSize = useCallback(() => {
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;

    setStageSize({
      width: canvasSize.width,
      height: canvasSize.height,
    });
  }, [platform]);

  const handleZoomChange = useCallback((newZoom: number) => {
    setIsManualZoom(true);
    setZoomLevel(newZoom);
  }, []);

  const fitToView = useCallback(() => {
    if (!canvasContainerRef.current) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const sceneWidth = canvasSize.width;
    const sceneHeight = canvasSize.height;

    const container = canvasContainerRef.current;
    const containerWidth = container.clientWidth - 80;
    const containerHeight = container.clientHeight - 80;

    const scaleX = containerWidth / sceneWidth;
    const scaleY = containerHeight / sceneHeight;
    const newZoom = Math.min(scaleX, scaleY, 1.25);

    setIsManualZoom(false);
    setZoomLevel(newZoom);
  }, [platform]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }
    const handleResize = () => {
      updateStageSize();
    };

    updateStageSize();

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen, updateStageSize]);

  useEffect(() => {
    if (!stageRef.current || !isOpen || isInitialized) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const canvasWidth = canvasSize.width;
    const canvasHeight = canvasSize.height;

    const stage = stageRef.current;
    stage.width(canvasWidth);
    stage.height(canvasHeight);

    setKonvaStage(stage);
    setIsInitialized(true);

    setTimeout(() => {
      if (canvasContainerRef.current) {
        fitToView();
        const container = canvasContainerRef.current;
        const scrollLeft = (container.scrollWidth - container.clientWidth) / 2;
        const scrollTop = (container.scrollHeight - container.clientHeight) / 2;
        container.scrollTo({
          left: scrollLeft,
          top: scrollTop,
          behavior: 'smooth',
        });
      }
    }, 300);

    setHistory([stage.toJSON()]);
    setHistoryStep(1);

    const saveStateDelayed = () => {
      setTimeout(() => {
        setHistory(prevHistory => {
          const newHistory = [...prevHistory.slice(0, historyStep)];
          newHistory.push(stage.toJSON());
          setHistoryStep(newHistory.length);
          return newHistory;
        });
      }, 100);
    };

    // Need to check if these are getting called
    stage.on('dragend', saveStateDelayed);
    stage.on('transformend', saveStateDelayed);

    if (initialImage) {
      const imageObj = new Image();
      imageObj.crossOrigin = 'anonymous';
      imageObj.onload = () => {
        let layer = stage.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          stage.add(layer);
        }

        const canvasWidth = stage.width();
        const canvasHeight = stage.height();
        const imgWidth = imageObj.width;
        const imgHeight = imageObj.height;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        const konvaImage = new Konva.Image({
          image: imageObj,
          x: (canvasWidth - imgWidth * scale) / 2,
          y: (canvasHeight - imgHeight * scale) / 2,
          scaleX: scale,
          scaleY: scale,
          draggable: true,
        });

        addCursorHandlers(konvaImage);

        layer.add(konvaImage);
        layer.batchDraw();

        setTimeout(() => {
          setHistory(prevHistory => {
            const newHistory = [...prevHistory.slice(0, historyStep)];
            newHistory.push(stage.toJSON());
            setHistoryStep(newHistory.length);
            return newHistory;
          });
        }, 100);
      };
      imageObj.src = initialImage;
    }

    return () => {
      stage.off('dragend', saveStateDelayed);
      stage.off('transformend', saveStateDelayed);
    };
  }, [isOpen, initialImage, platform, historyStep, fitToView, isInitialized]);

  useEffect(() => {
    if (!isOpen && isInitialized) {
      setIsInitialized(false);
      setKonvaStage(null);
      setZoomLevel(1);
      setIsManualZoom(false);
      setHistory([]);
      setHistoryStep(0);
    }
  }, [isOpen, isInitialized]);

  const deleteSelectedObjects = useCallback(() => {
    if (!konvaStage) {
      return;
    }

    const transformer = konvaStage.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      return;
    }

    const selectedNodes = transformer.nodes();
    if (selectedNodes.length === 0) {
      return;
    }

    selectedNodes.forEach((node) => {
      node.destroy();
    });

    transformer.nodes([]);
    konvaStage.batchDraw();

    setTimeout(() => {
      setHistory(prevHistory => {
        const newHistory = [...prevHistory.slice(0, historyStep)];
        newHistory.push(konvaStage.toJSON());
        setHistoryStep(newHistory.length);
        return newHistory;
      });
    }, 100);
  }, [konvaStage, historyStep]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        const target = e.target as HTMLElement;
        if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA' && !target.isContentEditable) {
          e.preventDefault();
          deleteSelectedObjects();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, deleteSelectedObjects]);


  const undo = useCallback(() => {
    setHistoryStep(prevStep => {
      if (prevStep > 1 && konvaStage) {
        const newStep = prevStep - 1;
        setHistory(prevHistory => {
          const state = prevHistory[newStep - 1];
          if (state) {
            konvaStage.destroy();
            const newStage = Konva.Node.create(state, stageRef.current?.container());
            setKonvaStage(newStage);
          }
          return prevHistory;
        });
        return newStep;
      }
      return prevStep;
    });
  }, [konvaStage]);

  const redo = useCallback(() => {
    setHistoryStep(prevStep => {
      setHistory(prevHistory => {
        if (prevStep < prevHistory.length && konvaStage) {
          const state = prevHistory[prevStep];
          if (state) {
            konvaStage.destroy();
            const newStage = Konva.Node.create(state, stageRef.current?.container());
            setKonvaStage(newStage);
            return prevHistory;
          }
        }
        return prevHistory;
      });
      return prevStep < history.length ? prevStep + 1 : prevStep;
    });
  }, [konvaStage, history.length]);

  const handleSaveDesign = async () => {
    if (!konvaStage) {
      console.error('Canvas not available');
      return;
    }

    if (!activeProject?.project_id) {
      console.error('No active project');
      toast.error('No active project found');
      return;
    }

    try {
      const dataURL = konvaStage.toDataURL({
        mimeType: 'image/png',
        quality: 1,
        pixelRatio: 1,
      });

      const response = await fetch(dataURL);
      const blob = await response.blob();
      const timestamp = Date.now();
      const fileName = `canvas-design-${timestamp}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });

      const formData = new FormData();
      formData.append('image', file);
      formData.append('planId', planId || 'canvas-save');

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const uploadEndpoint = `${baseUrl}/agents/${agentId}/upload-canvas-image`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload canvas image');
      }

      const uploadResult = await uploadResponse.json();

      if (uploadResult.success && uploadResult.filepath) {
        await projectImageStorage.addCreationImage(
          activeProject.project_id,
          agentId,
          uploadResult.filepath,
          fileName,
          planId,
          'Canvas Design',
        );

        window.dispatchEvent(new CustomEvent('projectImagesUpdated', {
          detail: { projectId: activeProject.project_id },
        }));

        onSave(uploadResult.filepath);
        toast.success('Canvas design saved successfully!');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error saving canvas design:', error);
      toast.error('Failed to save canvas design');
      onSave('');
    }
  };

  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = stageRef.current;
    if (e.target === stage) {
      const transformer = stage.findOne('Transformer') as Konva.Transformer;
      if (transformer) {
        transformer.nodes([]);
        stage.batchDraw();
      }
      return;
    }

    const clickedNode = e.target;
    if (clickedNode.getClassName() === 'Transformer') {
      return;
    }

    const layer = clickedNode.getLayer();
    if (!layer) {
      return;
    }
    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }
    transformer.nodes([clickedNode]);
    stage?.batchDraw();
  };

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-0 left-0 right-0 bottom-0 h-[calc(100vh)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasHeader
        onSaveDesign={handleSaveDesign}
      />

      <div className="flex flex-1 min-h-0 flex-col md:flex-row">
        <div className="block">
          <CanvasSidebar
            canvas={konvaStage}
            agentId={agentId}
            planId={planId}
            containerRef={canvasContainerRef}
            zoomLevel={zoomLevel}
            onClose={onClose}
          />
        </div>
        <div className="flex-1 bg-neutral-800 flex flex-col">
          <div
            ref={canvasContainerRef}
            className="flex-1 w-full h-full overflow-auto scroll-smooth"
          >
            <div
              className="flex items-center justify-center"
              style={{
                minHeight: `calc(100% + ${Math.max(200, stageSize.height * zoomLevel * 0.5)}px)`,
                minWidth: `calc(100% + ${Math.max(200, stageSize.width * zoomLevel * 0.5)}px)`,
                padding: '100px',
              }}
            >
              <div
                style={{
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center',
                  transition: isManualZoom ? 'none' : 'transform 0.2s ease-out',
                }}
              >
                <Stage
                  ref={stageRef}
                  onClick={handleStageClick}
                  width={stageSize.width}
                  height={stageSize.height}
                  className="block rounded-xl overflow-hidden bg-white shadow-2xl"
                >
                  <Layer />
                </Stage>
              </div>
            </div>
          </div>
          <div className="p-2 md:p-4 text-center text-gray-500 text-xs">
            <div className="flex flex-col items-center gap-1">
              <p>Select layer for more options | Double-click text to edit inline | Delete key to remove selected objects</p>
            </div>
          </div>
        </div>
      </div>

      <FloatingToolbar
        canvas={konvaStage}
        zoomLevel={zoomLevel}
        onZoomChange={handleZoomChange}
        onFitToView={fitToView}
        onUndo={undo}
        onRedo={redo}
        canUndo={historyStep > 1}
        canRedo={historyStep < history.length}
      />
    </div>
  );
};
